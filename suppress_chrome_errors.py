import os
import sys
import logging
from contextlib import redirect_stderr, redirect_stdout
from io import StringIO

class SuppressOutput:
    """Класс для подавления всех выводов Chrome"""
    def __init__(self):
        self.devnull = open(os.devnull, 'w')
        
    def __enter__(self):
        self.old_stdout = sys.stdout
        self.old_stderr = sys.stderr
        sys.stdout = self.devnull
        sys.stderr = self.devnull
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.old_stdout
        sys.stderr = self.old_stderr
        self.devnull.close()

def suppress_chrome_logs():
    """Подавляет все логи Chrome на системном уровне"""
    # Подавление логов через переменные окружения
    os.environ['CHROME_LOG_FILE'] = os.devnull
    os.environ['CHROME_HEADLESS'] = '1'
    
    # Отключение всех логов Chrome
    logging.getLogger('selenium').setLevel(logging.CRITICAL)
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)
    logging.getLogger('requests').setLevel(logging.CRITICAL)
    
    # Подавление предупреждений
    import warnings
    warnings.filterwarnings("ignore")
    
    # Перенаправление stderr в devnull для Chrome процессов
    if os.name == 'nt':  # Windows
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
def setup_chrome_options_silent():
    """Возвращает максимально тихие опции Chrome"""
    from selenium.webdriver.chrome.options import Options
    
    options = Options()
    
    # Основные опции для подавления ошибок
    silent_args = [
        "--headless=new",  # Новый headless режим
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-extensions",
        "--disable-plugins",
        "--disable-images",
        "--disable-javascript",
        
        # Полное отключение логирования
        "--log-level=3",
        "--silent",
        "--disable-logging",
        "--disable-gpu-logging",
        "--disable-dev-tools",
        
        # Отключение всех Google сервисов
        "--disable-background-networking",
        "--disable-sync",
        "--disable-default-apps",
        "--disable-component-extensions-with-background-pages",
        "--disable-component-update",
        "--disable-domain-reliability",
        "--disable-background-mode",
        "--disable-client-side-phishing-detection",
        
        # Отключение дополнительных функций
        "--disable-features=TranslateUI,MediaRouter,VizDisplayCompositor",
        "--disable-hang-monitor",
        "--disable-prompt-on-repost",
        "--disable-web-security",
        "--disable-field-trial-config",
        "--disable-back-forward-cache",
        "--disable-ipc-flooding-protection",
        "--disable-renderer-backgrounding",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        
        # Дополнительные опции для тишины
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-popup-blocking",
        "--disable-translate",
        "--disable-background-downloads",
        "--disable-add-to-shelf",
        "--disable-background-sync",
    ]
    
    for arg in silent_args:
        options.add_argument(arg)
    
    # Экспериментальные опции
    options.add_experimental_option('excludeSwitches', [
        'enable-logging', 
        'enable-automation',
        'enable-blink-features'
    ])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_experimental_option("detach", True)
    
    # Подавление логов через prefs
    prefs = {
        "profile.default_content_setting_values": {
            "notifications": 2,
            "media_stream": 2,
        },
        "profile.managed_default_content_settings": {
            "images": 2
        }
    }
    options.add_experimental_option("prefs", prefs)
    
    return options
