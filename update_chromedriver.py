#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Скрипт для автоматического обновления ChromeDriver.
Проверяет версию установленного Chrome и скачивает соответствующую версию ChromeDriver.
"""

from ChromeDriverUpdater import update_chromedriver_if_needed

if __name__ == "__main__":
    print("Запуск проверки и обновления ChromeDriver...")
    result = update_chromedriver_if_needed()
    if result:
        print("ChromeDriver успешно обновлен или уже соответствует версии Chrome.")
    else:
        print("Не удалось обновить ChromeDriver. Проверьте логи для получения дополнительной информации.")
