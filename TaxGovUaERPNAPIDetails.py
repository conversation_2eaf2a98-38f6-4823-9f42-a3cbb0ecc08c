import asyncio
from datetime import datetime
import time
import aiohttp
import numpy as np
import pandas as pd
import requests
import os
import warnings
from pathlib import Path

# Подавляем предупреждения pandas
warnings.filterwarnings('ignore', category=FutureWarning)
from AsyncPostgresql import get_df, async_save_pg, save_to_pg
print("AsyncPostgresql импортирован успешно")
from TaxGovUaConfig import get_token
print("TaxGovUaConfig импортирован успешно")
cur_dir = Path(__file__).resolve().parent
url_const = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkbd?code="
driver = None
token = None
wrong_urls = []


# Асинхронная функция для выполнения HTTP-запроса
async def fetch_async(url, session, semaphore, retries=3):
    global driver, token, wrong_urls
    if not driver or not token:
        driver, token = get_token()
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                # response = requests.get(url, headers=headers, timeout=20)
                # if response and response.status_code == 200:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        response = await response.json()
                        data = await convert_response(response)
                        result = await save_to_db_async(data)
                        print(f"Data saved: {result}")
                        break
                        # return response
                    elif response.status == 400:
                        # print(f"Помилка обробки запиту {url}: {response}")
                        break
            except aiohttp.ClientError as e:
                # print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                # print(f"Тайм-аут при запросе данных: {url}")
                pass

            # Экспоненциальная задержка перед повторной попыткой
            await asyncio.sleep(2**attempt)
            _, token = get_token(driver=driver)
    wrong_urls.append(url)
    print(f"count wrong_urls: {len(wrong_urls)}")
    return None


# Асинхронная функция для выполнения HTTP-запроса
async def fetch(url, retries=3):
    global driver, token, wrong_urls
    for attempt in range(retries):
        response = None
        if not driver or not token:
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                result = response.json()
                data = await convert_response(result)
                is_save = await save_to_db(data)
                # print(f"saved: {is_save}; url: {url}")
                return None
            else:
                pass
        except Exception as e:
            pass
        time.sleep(2**attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None

    print(f"count wrong_urls: {len(wrong_urls)}")
    wrong_urls.append(url)
    return None


# из базы postgresql извлекаем code и dend, для получения детализации по документам
async def get_data_from_df():
    sql = """SELECT * FROM t_tax_cabinet_erpn_api WHERE dend::date >= '01.01.2024'::date ORDER BY dend, code;"""
    df = await get_df(sql)
    df["dend"] = df["dend"].astype(str)
    result = df[["code", "dend"]].values.tolist()
    return result


# Создаем urls для получения детализации по документам
async def get_urls(data):
    return [f"{url_const}{r[0]}&dend={r[1]}%2000:00:00" for r in data]


# Асинхронная функция для запуска задач по 5 штук одновременно
async def run_tasks(urls, async_=True):
    semaphore = asyncio.Semaphore(2)  # Ограничение на 5 одновременных задач
    async with aiohttp.ClientSession() as session:
        if async_:
            tasks = [fetch_async(url, session, semaphore) for url in urls]
        else:
            tasks = [fetch(url) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


async def save_to_db(data):
    # сохраняем данные в базу
    sql = f"INSERT INTO t_tax_cabinet_erpn_api_details VALUES ({('%s,'*28)[:-1]}) ON CONFLICT (nncode, rnum) DO NOTHING;"
    return save_to_pg(sql, data)


async def save_to_db_async(data):
    # сохраняем данные в базу
    sql = """INSERT INTO t_tax_cabinet_erpn_api_details 
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21,
              $22, $23, $24, $25, $26, $27, $28)
             ON CONFLICT (nncode, rnum) DO NOTHING
        ;"""
    return await async_save_pg(sql, data)


# Преобразование данных в нужный формат
async def convert_response(responses):
    # data = [row for rows in responses for row in rows]
    df = pd.DataFrame(responses)
    df["dend"] = pd.to_datetime(df["dend"], errors="coerce")
    df["crtdate"] = pd.to_datetime(df["crtdate"], errors="coerce")
    df["rg4"] = df["rg4"].fillna(0).astype(np.int64)
    df["docrnn"] = df["docrnn"].fillna(0).astype(np.int64)
    df["d2rg5"] = df["d2rg5"].fillna(0).infer_objects(copy=False)
    df["d2rg6"] = df["d2rg6"].fillna(0).infer_objects(copy=False)
    df["rg5"] = df["rg5"].fillna(0).infer_objects(copy=False)
    df["rg6"] = df["rg6"].fillna(0).infer_objects(copy=False)
    data = [tuple(row) for row in df.values]
    return data


# сохраняем проблемные URL в файл
# старый файл удаляем
def save_wrong_urls():
    file_path = os.path.join(cur_dir, "wrong_urls.txt")
    if os.path.exists(file_path):
        os.remove(file_path)
    with open(file_path, "w") as f:
        f.write("\n".join(wrong_urls))

# читаем проблемные URL из файла
def read_wrong_urls_from_file():
    file_path = os.path.join(cur_dir, "wrong_urls.txt")
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return f.read().split("\n")
    return []


async def read_wrong_urls():
    global wrong_urls, driver, token
    wrong_urls = read_wrong_urls_from_file()
    repeat_count = 1
    while len(wrong_urls) > 0 and wrong_urls[0]:
        save_wrong_urls()
        if driver:
            driver.quit()
        driver, token = get_token()
        await asyncio.sleep(10 * repeat_count)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        await run_tasks(urls, async_=False)
    save_wrong_urls()


# точка входа
async def main_taxgovua_erpn_api_details():
    global driver, token, wrong_urls

    print("Начало выполнения main_taxgovua_erpn_api_details()")
    print("Чтение проблемных URL...")
    await read_wrong_urls()
    print("Проблемные URL обработаны")

    # получаем данные из базы, для получения детализации по документам
    data_from_db = await get_data_from_df()
    if not data_from_db:
        print("Ошибка получения данных из базы")
        return

    # создаем urls для получения детализации по документам
    urls = await get_urls(data_from_db)

    # запускаем задачи - загружаем данные из сайта и сохраняем в базу
    await run_tasks(urls, async_=False)

    await read_wrong_urls()
    if driver:
        driver.quit()


if __name__ == "__main__":
    print("Start", datetime.now())
    print("Запуск скрипта TaxGovUaERPNAPIDetails...")
    print("Вызов asyncio.run...")
    try:
        asyncio.run(main_taxgovua_erpn_api_details())
    except Exception as e:
        print(f"Ошибка в main: {e}")
    print("End", datetime.now())
