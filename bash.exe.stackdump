Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x1FEBA
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210285FF9, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9B80  0002100690B4 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9E60  00021006A49D (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE973E0000 ntdll.dll
7FFE95E20000 KERNEL32.DLL
7FFE94530000 KERNELBASE.dll
7FFE952F0000 USER32.dll
7FFE94BE0000 win32u.dll
000210040000 msys-2.0.dll
7FFE96E70000 GDI32.dll
7FFE94C10000 gdi32full.dll
7FFE94920000 msvcp_win.dll
7FFE94EB0000 ucrtbase.dll
7FFE95D00000 advapi32.dll
7FFE95180000 msvcrt.dll
7FFE96DC0000 sechost.dll
7FFE954C0000 RPCRT4.dll
7FFE93A40000 CRYPTBASE.DLL
7FFE94D50000 bcryptPrimitives.dll
7FFE95C10000 IMM32.DLL
